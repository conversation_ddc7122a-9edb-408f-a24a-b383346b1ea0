import numpy as np

# Create a 2D NumPy array
arr = np.array([
                    ["a", "b", "c"],
                    ["d", "e", "f"],
                    ["g","h","i"],
                ])

# Print the array
print("Array:")
print(arr)

# Display array properties
print("\nShape of the array:", arr.shape) # (rows, columns)
print("Number of dimensions:", arr.ndim)  # ndim shows 1D, 2D, etc.
print("Size (total elements):", arr.size) # total number of elements