import numpy as np

"""
np.intersect1d - find common elements in two arrays
np.setdiff1d - find elements in one array that are not in another
"""
a = np.array([1, 2, 3, 4, 5])
b = np.array([4, 5, 6, 7, 8])

common = np.intersect1d(a, b)
diff = np.setdiff1d(a, b)
diff2 = np.setdiff1d(b, a)
print("Intersection:", common)
print("In a but not in b:", diff)
print("In b but not in a:", diff2)

# ----------------------------------------------------------------

