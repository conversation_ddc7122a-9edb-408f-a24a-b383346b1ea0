import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from sklearn.linear_model import LinearRegression

df = pd.read_csv('./study/project/房价预测模型（机器学习入门）/house.csv')

# plt.scatter(df['Area'], df['Price'])
# plt.xlabel('Area (sqft)')
# plt.ylabel('Price (k RM)')
# plt.title('Area vs Price')
# plt.grid(True)
# plt.show()

x = df[['Area']]
y = df['Price']

model = LinearRegression()
model.fit(x, y)

print("斜率 (a):", model.coef_[0])
print("截距 (b):", model.intercept_)

area_test = np.array([[1700]])
predicted_price = model.predict(area_test)
print("预测价格（面积 1700）:", predicted_price[0])
