# import numpy as np

# # np.zeros() - create an array filled with zeros
# arr = np.zeros((2, 3))
# print(arr)

# ----------------------------------------------------------------

# import numpy as np

# """
# np.zeros() - create an array filled with zeros
# np.full() - create an array filled with a constant value
# """
# arr = np.zeros((2, 2))
# arr2 = np.full((3, 2), 'a')
# print("np.zeros((2, 2)):\n",arr)
# print("np.full((3, 2), 'a'):\n", arr2)

# ----------------------------------------------------------------

# import numpy as np

# """
# np.arange() - create an array filled with arange(start, stop, step)
# """
# arr = np.arange(0, 10)
# arr2 = np.arange(-10, 10, 2)
# print("np.arange(0, 10):\n", arr)
# print("np.arange(-10, 10, 2):\n", arr2)

# ----------------------------------------------------------------

import numpy as np

"""
np.linspace() - create an array filled with arange(start, stop, num) num -> with x value(s)
"""
arr = np.linspace(0, 1, 5)
arr2 = np.linspace(0, 2, 5)
arr3 = np.linspace(0, 100, 5)
arr4 = np.linspace(0, 10, 4)
print("np.linspace(0, 1, 5):\n", arr)
print("np.linspace(0, 2, 5):\n", arr2)
print("np.linspace(0, 100, 5):\n", arr3)
print("np.linspace(0, 10, 5):\n", arr4)

# ----------------------------------------------------------------