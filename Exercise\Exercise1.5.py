import numpy as np

arr1 = np.array([10, 20, 30, 40, 50])
arr2 = np.array([30, 40, 50, 60, 70])
arr3 = np.array([50, 60, 70, 80, 90])

common1 = np.intersect1d(arr1, arr2)
common2 = np.intersect1d(common1, arr3)

diff1 = np.setdiff1d(arr1, arr2)
diff2 = np.setdiff1d(arr2, arr3)

print(common1)
print('--------------------------')
print(common2)
print('--------------------------')
print(diff1)
print('--------------------------')
print(diff2)